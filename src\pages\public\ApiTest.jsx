import { useState } from 'react';
import api from '../../services/api';

const ApiTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    const results = [];

    // Test 1: Basic connectivity
    try {
      const response = await fetch('http://localhost:8000/api/locations/');
      results.push({
        test: 'Basic fetch to /api/locations/',
        status: 'SUCCESS',
        details: `Status: ${response.status}`,
        data: response.status === 200 ? 'Connected' : 'Error'
      });
    } catch (error) {
      results.push({
        test: 'Basic fetch to /api/locations/',
        status: 'FAILED',
        details: error.message,
        data: null
      });
    }

    // Test 2: Using our API service
    try {
      const response = await api.get('/api/locations/');
      results.push({
        test: 'API service to /api/locations/',
        status: 'SUCCESS',
        details: `Status: ${response.status}`,
        data: response.data
      });
    } catch (error) {
      results.push({
        test: 'API service to /api/locations/',
        status: 'FAILED',
        details: error.message,
        data: error.response?.data || null
      });
    }

    // Test 3: Login endpoint test
    try {
      const response = await fetch('http://localhost:8000/api/accounts/login/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'testpassword'
        })
      });
      results.push({
        test: 'Login endpoint test',
        status: response.status === 400 || response.status === 401 ? 'SUCCESS' : 'UNKNOWN',
        details: `Status: ${response.status} (400/401 expected for invalid credentials)`,
        data: response.status
      });
    } catch (error) {
      results.push({
        test: 'Login endpoint test',
        status: 'FAILED',
        details: error.message,
        data: null
      });
    }

    setTestResults(results);
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">API Connectivity Test</h1>
          
          <button
            onClick={runTests}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium mb-6"
          >
            {loading ? 'Running Tests...' : 'Run API Tests'}
          </button>

          {testResults.length > 0 && (
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900">Test Results:</h2>
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`border rounded-lg p-4 ${
                    result.status === 'SUCCESS'
                      ? 'border-green-200 bg-green-50'
                      : result.status === 'FAILED'
                      ? 'border-red-200 bg-red-50'
                      : 'border-yellow-200 bg-yellow-50'
                  }`}
                >
                  <h3 className="font-medium text-gray-900">{result.test}</h3>
                  <p className={`text-sm ${
                    result.status === 'SUCCESS'
                      ? 'text-green-600'
                      : result.status === 'FAILED'
                      ? 'text-red-600'
                      : 'text-yellow-600'
                  }`}>
                    Status: {result.status}
                  </p>
                  <p className="text-sm text-gray-600">Details: {result.details}</p>
                  {result.data && (
                    <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                      {typeof result.data === 'string' ? result.data : JSON.stringify(result.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))}
            </div>
          )}

          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">Troubleshooting Steps:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>1. Ensure the backend server is running on http://localhost:8000</li>
              <li>2. Check if the backend has CORS configured for http://localhost:5174</li>
              <li>3. Verify the API endpoints are correct</li>
              <li>4. Check network connectivity</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiTest;
