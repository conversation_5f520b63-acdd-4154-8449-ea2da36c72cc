**Frontend Development Prompt: Kigali GasGo – Fresh React Rebuild**

**Project Name:** Kigali GasGo (GasGo Rwanda)
**Frontend Framework:** React with Vite (⚠️ no TypeScript – use .jsx only)
**Styling:** Tailwind CSS
**Start From:** Scratch – this is a **fresh React + Vite project**, not a Vue conversion
**Backend API:** [gas\_stock\_management\_backend](https://github.com/Alicelinzy/gas_stock_management_backend)
**Docker Backend:** Yes – Docker-based backend is working
**API Documentation:**
→ [API Docs in Repo](https://github.com/Alicelinzy/gas_stock_management_backend/blob/main/backend/docs/gas_management/api_documentation.md)
→ Endpoints listed in full below (updated to match Postman)

---

### 1. 🧹 **Goal and Context**

Build a brand new frontend using **React + Vite** for the Kigali GasGo system – a platform for gas delivery and stock management. The frontend must integrate tightly with the backend's API and support 3 user roles: **Buyers, Sellers, and Admins**.

The Vue project is deprecated – start clean with modern React.

---

### 2. ⚙️ **Tech Stack and Architecture**

* **React + Vite** (lightweight and fast builds)
* **Tailwind CSS** for styling
* **React Router v6** for routing
* **Axios** for API requests
* **JWT Auth**: token stored in `localStorage`
* Role-based routing and route guards
* Use `.env` for `VITE_API_BASE_URL`

---

### 3. 👥 **User Roles and Core Pages**

#### Public:

* `/` – Landing page
* `/signup` – Sign up (Buyer or Seller)
* `/login` – Login

#### Buyer:

* `/buyer/dashboard` – View gas products
* `/buyer/order` – Place an order
* `/buyer/orders` – Track orders
* `/buyer/ratings/order/:id` – Rate order

#### Seller:

* `/seller/dashboard` – Stats overview
* `/seller/inventory` – Manage gas stock
* `/seller/orders` – Fulfill orders
* `/seller/invoice/generate/:id` – Generate invoice

#### Admin:

* `/admin/dashboard` – Overview
* `/admin/orders` – Review and verify orders
* `/admin/invoices` – Approve invoices
* `/admin/users` – Manage users
* `/admin/reports` – See system activity

---

### 4. 🔌 **Backend API Integration**

Backend: [`https://github.com/Alicelinzy/gas_stock_management_backend`](https://github.com/Alicelinzy/gas_stock_management_backend)
Docs: [`API Documentation`](https://github.com/Alicelinzy/gas_stock_management_backend/blob/main/backend/docs/gas_management/api_documentation.md)

Use the actual endpoint structure provided. Axios interceptors must attach JWTs to all authenticated requests.

---

### 5. 🗂️ **API Endpoints Summary (Excluding Accounts)**

#### 📍 Locations

* `POST /api/locations/create/` – Create new location
* `GET /api/locations/` – List all locations
* `PUT /api/locations/{id}/` – Update location
* `DELETE /api/locations/{id}/` – Delete location

Example response (GET):

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Kacyiru",
      "district": "Gasabo",
      "city": "Kigali",
      "sector": "Kimihurura"
    }
  ]
}
```

#### 📊 Inventory

* `POST /api/invetory/create/` – Add inventory item
* `GET /api/invetory/all/` – Get inventory list
* `PUT /api/invetory/{id}/update/` – Update inventory
* `DELETE /api/invetory/{id}/delete/` – Delete inventory

Example response (POST):

```json
{
  "status": "success",
  "message": "Inventory added",
  "data": {
    "id": 1,
    "brand": "SP0Gas",
    "weight_kg": 12.5,
    "quantity": 10,
    "unit_price": "15000.00",
    "location": 2
  }
}
```

#### 🛒 Orders (Bookings)

* `POST /api/booking/orders/create/` – Place an order
* `GET /api/booking/orders/` – View all orders (Admin)
* `GET /api/booking/orders/seller/` – View seller's orders
* `POST /api/booking/orders/{id}/approve/` – Approve order
* `POST /api/booking/orders/{id}/reject/` – Reject order
* `POST /api/booking/orders/{id}/mark-delivered/` – Mark as delivered
* `PUT /api/booking/orders/{id}/update/` – Update order
* `DELETE /api/booking/orders/{id}/delete/` – Delete order

Example response (Create):

```json
{
  "status": "success",
  "message": "Order created",
  "data": {
    "id": 3,
    "quantity": 2,
    "status": "PENDING",
    "total_price": "30000.00",
    "delivery_address": "Kimihurura",
    "contact_phone": "0788000000"
  }
}
```

---

### 6. 📀 UI/UX Guidelines

* Responsive: Mobile, tablet, desktop
* Validation for forms
* Loading spinners and error messages
* Use Tailwind utilities (`grid`, `flex`, `gap`, etc.)
* Reusable components:

  * `<ProductCard />`
  * `<Sidebar />`
  * `<DashboardStats />`
  * `<OrderTable />`

---

### 7. ✅ Final Instructions

* Fully replace Vue frontend with **clean React app**
* Use **React functional components** only
* All routes must work with real backend APIs
* Ensure Docker backend integration works locally
* Use clear, modular structure: `/pages`, `/components`, `/services`
* Keep the UI clean and functional — no over-design needed

---
