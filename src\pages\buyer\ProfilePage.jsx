import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import  authService from "../../services/auth";


export default function ProfilePage() {
  const [profile, setProfile] = useState({
    first_name: '',
    last_name: '',
    email: ''
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const data = await authService.getProfile();
        setProfile({
          first_name: data.first_name || '',
          last_name: data.last_name || '',
          email: data.email || ''
        });
      } catch (error) {
        const errorMessage = error?.response?.data?.message || 'Failed to load profile';
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };
    fetchProfile();
  }, []);

  const handleChange = (e) => {
    setProfile({ ...profile, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await authService.updateProfile(profile);
      toast.success('Profile updated!');
    } catch (error) {
      const errorMessage = error?.response?.data?.message || 'Update failed';
      toast.error(errorMessage);
    }
  };

  if (loading) return <div className="text-center mt-10">Loading profile...</div>;

  return (
    <div className="p-4 max-w-md mx-auto">
      <h2 className="text-xl font-semibold mb-4 text-center">My Profile</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <input
          type="text"
          name="first_name"
          value={profile.first_name}
          onChange={handleChange}
          placeholder="First Name"
          className="w-full p-2 border rounded"
        />
        <input
          type="text"
          name="last_name"
          value={profile.last_name}
          onChange={handleChange}
          placeholder="Last Name"
          className="w-full p-2 border rounded"
        />
        <input
          type="email"
          name="email"
          value={profile.email}
          disabled
          className="w-full p-2 border rounded bg-gray-100"
        />
        <button
          type="submit"
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded"
        >
          Update Profile
        </button>
      </form>
    </div>
  );
}