import api from './api';

export const authService = {
  // Register new user
  register: async (userData) => {
    
    const response = await api.post( '/api/accounts/register/', userData);
    return response.data;
  },

  // Login user
 login: async (credentials) => {
  const response = await api.post('/api/accounts/login/', credentials);
  const { data } = response.data; // Extract "data" from the whole response

  if (data?.access) {
    localStorage.setItem('token', data.access);
    localStorage.setItem('refresh_token', data.refresh);
    localStorage.setItem('user', JSON.stringify(data)); // Contains role, name, email etc.
  }

  return {
    access: data?.access,
    refresh: data?.refresh,
    user: data, // This includes role, first_name, etc.
  };
},

  // login: async (credentials) => {
  //   const response = await api.post('/api/accounts/login/', credentials);
  //   if (response.data.access) {
  //     localStorage.setItem('token', response.data.access); // using JWT 'access' token
  //     localStorage.setItem('user', JSON.stringify(response.data.user));
  //   }
  //   return response.data;
  // },

  // Logout user
  logout: async () => {
    try {
      await api.post('/api/accounts/logout/');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear all authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
    }
  },

  // Get user profile
  getProfile: async () => {
    const response = await api.get('/api/accounts/profile/');
    return response.data;
  },

  // Update user profile
  updateProfile: async (profileData) => {
    const response = await api.put('/api/accounts/profile/update/', profileData);
    return response.data;
  },

  // Change password
  changePassword: async (passwordData) => {
    const response = await api.post('/api/accounts/change-password/', passwordData);
    return response.data;
  },

  // Get current user from localStorage
  getCurrentUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return !!localStorage.getItem('token');
  },

  // Get user role
  getUserRole: () => {
    const user = authService.getCurrentUser();
    return user?.role || null;
  }
};
