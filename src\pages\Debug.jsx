import { useState } from 'react';
import api from '../services/api';

const Debug = () => {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState(false);

  const testEndpoint = async (name, endpoint) => {
    try {
      setResults(prev => ({ ...prev, [name]: { status: 'testing', data: null, error: null } }));
      const response = await api.get(endpoint);
      setResults(prev => ({ 
        ...prev, 
        [name]: { 
          status: 'success', 
          data: response.data, 
          error: null,
          statusCode: response.status 
        } 
      }));
    } catch (error) {
      setResults(prev => ({ 
        ...prev, 
        [name]: { 
          status: 'error', 
          data: null, 
          error: {
            message: error.message,
            code: error.code,
            status: error.response?.status,
            data: error.response?.data
          }
        } 
      }));
    }
  };

  const testAllEndpoints = async () => {
    setLoading(true);
    setResults({});

    const endpoints = [
      { name: 'Locations', endpoint: '/api/locations/' },
      { name: 'Inventory', endpoint: '/api/invetory/all/' },
      { name: 'Orders', endpoint: '/api/booking/orders/' },
      { name: 'Accounts Users', endpoint: '/api/accounts/users/' }
    ];

    for (const { name, endpoint } of endpoints) {
      await testEndpoint(name, endpoint);
      // Add small delay between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setLoading(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'testing': return 'text-yellow-600 bg-yellow-50';
      case 'success': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">API Debug Console</h1>
          
          {/* Configuration Info */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">Configuration</h2>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">API Base URL:</span> {import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'}</p>
              <p><span className="font-medium">Environment:</span> {import.meta.env.MODE}</p>
              <p><span className="font-medium">Token:</span> {localStorage.getItem('token') ? 'Present' : 'Not found'}</p>
            </div>
          </div>

          {/* Test Button */}
          <div className="mb-6">
            <button
              onClick={testAllEndpoints}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md font-medium"
            >
              {loading ? 'Testing Endpoints...' : 'Test All Endpoints'}
            </button>
          </div>

          {/* Results */}
          <div className="space-y-4">
            {Object.entries(results).map(([name, result]) => (
              <div key={name} className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold">{name}</h3>
                  <span className="text-sm font-medium">
                    {result.status === 'testing' && 'Testing...'}
                    {result.status === 'success' && `✅ ${result.statusCode}`}
                    {result.status === 'error' && `❌ Error`}
                  </span>
                </div>
                
                {result.status === 'success' && (
                  <div className="text-sm">
                    <p className="font-medium mb-1">Response:</p>
                    <pre className="bg-white p-2 rounded text-xs overflow-auto max-h-32">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}
                
                {result.status === 'error' && (
                  <div className="text-sm">
                    <p className="font-medium mb-1">Error Details:</p>
                    <div className="bg-white p-2 rounded text-xs">
                      <p><span className="font-medium">Message:</span> {result.error.message}</p>
                      {result.error.code && <p><span className="font-medium">Code:</span> {result.error.code}</p>}
                      {result.error.status && <p><span className="font-medium">Status:</span> {result.error.status}</p>}
                      {result.error.data && (
                        <div className="mt-2">
                          <p className="font-medium">Response Data:</p>
                          <pre className="mt-1 overflow-auto max-h-20">
                            {JSON.stringify(result.error.data, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Instructions */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Troubleshooting</h2>
            <div className="text-sm text-gray-700 space-y-2">
              <p><strong>If you see "ERR_NETWORK" errors:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Make sure the backend server is running on http://localhost:8000</li>
                <li>Check if Docker containers are up and running</li>
                <li>Verify CORS settings in the backend</li>
              </ul>
              
              <p className="mt-3"><strong>If you see 401 errors:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>You need to login first to get an authentication token</li>
                <li>Some endpoints require authentication</li>
              </ul>
              
              <p className="mt-3"><strong>If you see 404 errors:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>The endpoint might not exist in the backend</li>
                <li>Check the API documentation for correct endpoint paths</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Debug;
