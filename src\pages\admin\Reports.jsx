import { useState, useEffect } from 'react';
import { adminService } from '../../services/endpoints';

const AdminReports = () => {
  const [reportData, setReportData] = useState({
    orders: [],
    users: [],
    invoices: [],
    totalRevenue: 0,
    monthlyRevenue: 0,
    orderStats: {},
    userStats: {}
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dateRange, setDateRange] = useState('30'); // days

  useEffect(() => {
    fetchReportData();
  }, [dateRange]);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      setError(''); // Clear previous errors

      // Fetch data with individual error handling
      let orders = [];
      let users = [];
      let invoices = [];

      try {
        const ordersResponse = await adminService.getOrders();
        orders = Array.isArray(ordersResponse.data) ? ordersResponse.data : (Array.isArray(ordersResponse) ? ordersResponse : []);
      } catch (error) {
        console.warn('Failed to fetch orders for reports:', error.message);
      }

      try {
        const usersResponse = await adminService.getUsers();
        users = Array.isArray(usersResponse.data) ? usersResponse.data : (Array.isArray(usersResponse) ? usersResponse : []);
      } catch (error) {
        console.warn('Failed to fetch users for reports:', error.message);
      }

      try {
        const invoicesResponse = await adminService.getInvoices();
        invoices = Array.isArray(invoicesResponse.data) ? invoicesResponse.data : (Array.isArray(invoicesResponse) ? invoicesResponse : []);
      } catch (error) {
        console.warn('Failed to fetch invoices for reports:', error.message);
      }

      // Calculate statistics
      const totalRevenue = orders.reduce((sum, order) => 
        sum + parseFloat(order.total_price || 0), 0
      );

      // Filter by date range
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - parseInt(dateRange));

      const recentOrders = orders.filter(order => 
        new Date(order.created_at || Date.now()) >= cutoffDate
      );

      const monthlyRevenue = recentOrders.reduce((sum, order) => 
        sum + parseFloat(order.total_price || 0), 0
      );

      // Order statistics
      const orderStats = {
        total: orders.length,
        pending: orders.filter(o => o.status === 'PENDING').length,
        approved: orders.filter(o => o.status === 'APPROVED').length,
        delivered: orders.filter(o => o.status === 'DELIVERED').length,
        rejected: orders.filter(o => o.status === 'REJECTED').length
      };

      // User statistics
      const userStats = {
        total: users.length,
        buyers: users.filter(u => u.role === 'Buyer').length,
        sellers: users.filter(u => u.role === 'Seller').length,
        admins: users.filter(u => u.role === 'Admin').length
      };

      setReportData({
        orders,
        users,
        invoices,
        totalRevenue,
        monthlyRevenue,
        orderStats,
        userStats
      });
    } catch (error) {
      setError('Failed to fetch some report data. Please check if the backend is running.');
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-RW', {
      style: 'currency',
      currency: 'RWF',
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
        <div className="flex space-x-3">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="365">Last year</option>
          </select>
          <button
            onClick={fetchReportData}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
          >
            Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Revenue Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100">Total Revenue</p>
              <p className="text-3xl font-bold">{formatCurrency(reportData.totalRevenue)}</p>
            </div>
            <div className="p-3 bg-blue-400 rounded-full">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100">Period Revenue</p>
              <p className="text-3xl font-bold">{formatCurrency(reportData.monthlyRevenue)}</p>
              <p className="text-green-100 text-sm">Last {dateRange} days</p>
            </div>
            <div className="p-3 bg-green-400 rounded-full">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100">Total Orders</p>
              <p className="text-3xl font-bold">{reportData.orderStats.total}</p>
            </div>
            <div className="p-3 bg-purple-400 rounded-full">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Order Statistics */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Order Statistics</h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <p className="text-2xl font-bold text-gray-900">{reportData.orderStats.total}</p>
            <p className="text-sm text-gray-600">Total Orders</p>
          </div>
          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <p className="text-2xl font-bold text-yellow-600">{reportData.orderStats.pending}</p>
            <p className="text-sm text-gray-600">Pending</p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <p className="text-2xl font-bold text-green-600">{reportData.orderStats.approved}</p>
            <p className="text-sm text-gray-600">Approved</p>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <p className="text-2xl font-bold text-blue-600">{reportData.orderStats.delivered}</p>
            <p className="text-sm text-gray-600">Delivered</p>
          </div>
          <div className="text-center p-4 bg-red-50 rounded-lg">
            <p className="text-2xl font-bold text-red-600">{reportData.orderStats.rejected}</p>
            <p className="text-sm text-gray-600">Rejected</p>
          </div>
        </div>
      </div>

      {/* User Statistics */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">User Statistics</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <p className="text-2xl font-bold text-gray-900">{reportData.userStats.total}</p>
            <p className="text-sm text-gray-600">Total Users</p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <p className="text-2xl font-bold text-green-600">{reportData.userStats.buyers}</p>
            <p className="text-sm text-gray-600">Buyers</p>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <p className="text-2xl font-bold text-blue-600">{reportData.userStats.sellers}</p>
            <p className="text-sm text-gray-600">Sellers</p>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <p className="text-2xl font-bold text-purple-600">{reportData.userStats.admins}</p>
            <p className="text-sm text-gray-600">Admins</p>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Orders</h2>
          <div className="space-y-3">
            {reportData.orders.slice(0, 5).map((order) => (
              <div key={order.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">Order #{order.id}</p>
                  <p className="text-sm text-gray-600">{order.customer_name || 'Customer'}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">{formatCurrency(order.total_price || 0)}</p>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    order.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                    order.status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                    order.status === 'DELIVERED' ? 'bg-blue-100 text-blue-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {order.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Users */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Users</h2>
          <div className="space-y-3">
            {reportData.users.slice(0, 5).map((user) => (
              <div key={user.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{user.first_name || user.name || user.email}</p>
                  <p className="text-sm text-gray-600">{user.email}</p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  user.role === 'Admin' ? 'bg-purple-100 text-purple-800' :
                  user.role === 'Seller' ? 'bg-blue-100 text-blue-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {user.role}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminReports;
