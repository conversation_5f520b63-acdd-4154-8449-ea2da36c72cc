import { useState, useEffect } from 'react';
import { sellerService, invoiceService } from '../../services/endpoints';

const GenerateInvoice = () => {
  const [orders, setOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [invoiceData, setInvoiceData] = useState({
    order_id: '',
    invoice_number: '',
    due_date: '',
    notes: '',
    tax_rate: 18, // Default VAT rate
    discount: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [generatedInvoice, setGeneratedInvoice] = useState(null);

  useEffect(() => {
    fetchDeliveredOrders();
    generateInvoiceNumber();
  }, []);

  const fetchDeliveredOrders = async () => {
    try {
      const response = await sellerService.getOrders();
      const deliveredOrders = (response.data || response || []).filter(
        order => order.status?.toLowerCase() === 'delivered'
      );
      setOrders(deliveredOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);
      setError('Failed to load delivered orders.');
    }
  };

  const generateInvoiceNumber = () => {
    const timestamp = Date.now();
    const invoiceNumber = `INV-${timestamp}`;
    setInvoiceData(prev => ({ ...prev, invoice_number: invoiceNumber }));
  };

  const handleOrderSelect = (order) => {
    setSelectedOrder(order);
    setInvoiceData(prev => ({
      ...prev,
      order_id: order.id,
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
    }));
  };

  const calculateInvoiceTotal = () => {
    if (!selectedOrder) return 0;
    
    const subtotal = parseFloat(selectedOrder.total_amount || selectedOrder.total || 0);
    const discountAmount = (subtotal * invoiceData.discount) / 100;
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = (taxableAmount * invoiceData.tax_rate) / 100;
    
    return {
      subtotal: subtotal.toFixed(2),
      discount: discountAmount.toFixed(2),
      taxAmount: taxAmount.toFixed(2),
      total: (taxableAmount + taxAmount).toFixed(2)
    };
  };

  const handleGenerateInvoice = async (e) => {
    e.preventDefault();
    
    if (!selectedOrder) {
      setError('Please select an order first.');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const totals = calculateInvoiceTotal();
      
      const invoicePayload = {
        ...invoiceData,
        order_id: selectedOrder.id,
        customer_name: selectedOrder.customer_name || selectedOrder.user?.first_name || 'N/A',
        customer_email: selectedOrder.customer_email || selectedOrder.user?.email || '',
        subtotal: totals.subtotal,
        discount_amount: totals.discount,
        tax_amount: totals.taxAmount,
        total_amount: totals.total,
        status: 'pending'
      };

      const response = await invoiceService.createInvoice(invoicePayload);
      setGeneratedInvoice(response.data || response);
      setSuccess('Invoice generated successfully!');
      
      // Reset form
      setSelectedOrder(null);
      setInvoiceData({
        order_id: '',
        invoice_number: '',
        due_date: '',
        notes: '',
        tax_rate: 18,
        discount: 0
      });
      generateInvoiceNumber();
      
    } catch (error) {
      console.error('Error generating invoice:', error);
      setError('Failed to generate invoice. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePrintInvoice = () => {
    if (generatedInvoice) {
      window.print();
    }
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Generate Invoice</h1>
        <p className="mt-2 text-gray-600">Create invoices for delivered orders</p>
      </div>

      {error && (
        <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {success}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Order Selection */}
        <div className="bg-white shadow-lg rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Select Order</h2>
          
          {orders.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">📦</div>
              <p className="text-gray-500">No delivered orders available for invoicing.</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {orders.map((order) => (
                <div
                  key={order.id}
                  onClick={() => handleOrderSelect(order)}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedOrder?.id === order.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium text-gray-900">Order #{order.id}</p>
                      <p className="text-sm text-gray-600">
                        Customer: {order.customer_name || order.user?.first_name || 'N/A'}
                      </p>
                      <p className="text-sm text-gray-600">
                        Product: {order.product_name || order.product?.name || 'N/A'}
                      </p>
                      <p className="text-sm text-gray-600">
                        Date: {order.created_at ? new Date(order.created_at).toLocaleDateString() : 'N/A'}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        {order.total_amount || order.total || 'N/A'} RWF
                      </p>
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        Delivered
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Invoice Form */}
        <div className="bg-white shadow-lg rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Invoice Details</h2>
          
          <form onSubmit={handleGenerateInvoice} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Invoice Number</label>
              <input
                type="text"
                value={invoiceData.invoice_number}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, invoice_number: e.target.value }))}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Due Date</label>
              <input
                type="date"
                value={invoiceData.due_date}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, due_date: e.target.value }))}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Tax Rate (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={invoiceData.tax_rate}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, tax_rate: parseFloat(e.target.value) || 0 }))}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Discount (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={invoiceData.discount}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, discount: parseFloat(e.target.value) || 0 }))}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Notes</label>
              <textarea
                value={invoiceData.notes}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Additional notes or terms..."
              />
            </div>

            {/* Invoice Preview */}
            {selectedOrder && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-3">Invoice Preview</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>{calculateInvoiceTotal().subtotal} RWF</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Discount ({invoiceData.discount}%):</span>
                    <span>-{calculateInvoiceTotal().discount} RWF</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax ({invoiceData.tax_rate}%):</span>
                    <span>{calculateInvoiceTotal().taxAmount} RWF</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg border-t pt-2">
                    <span>Total:</span>
                    <span>{calculateInvoiceTotal().total} RWF</span>
                  </div>
                </div>
              </div>
            )}

            <button
              type="submit"
              disabled={loading || !selectedOrder}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Generating...' : 'Generate Invoice'}
            </button>
          </form>
        </div>
      </div>

      {/* Generated Invoice Display */}
      {generatedInvoice && (
        <div className="mt-8 bg-white shadow-lg rounded-lg p-6 print:shadow-none">
          <div className="flex justify-between items-center mb-6 print:mb-4">
            <h2 className="text-2xl font-bold text-gray-900">Invoice Generated</h2>
            <button
              onClick={handlePrintInvoice}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 print:hidden"
            >
              Print Invoice
            </button>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-6">
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold text-gray-900">INVOICE</h1>
              <p className="text-gray-600">#{generatedInvoice.invoice_number}</p>
            </div>
            
            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="font-semibold text-gray-900">From:</h3>
                <p className="text-gray-600">GasGo Seller</p>
                <p className="text-gray-600">Kigali, Rwanda</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">To:</h3>
                <p className="text-gray-600">{generatedInvoice.customer_name}</p>
                <p className="text-gray-600">{generatedInvoice.customer_email}</p>
              </div>
            </div>
            
            <div className="mb-6">
              <p><strong>Invoice Date:</strong> {new Date().toLocaleDateString()}</p>
              <p><strong>Due Date:</strong> {new Date(generatedInvoice.due_date).toLocaleDateString()}</p>
            </div>
            
            <div className="border-t border-b border-gray-200 py-4 mb-4">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>{generatedInvoice.subtotal} RWF</span>
              </div>
              <div className="flex justify-between">
                <span>Discount:</span>
                <span>-{generatedInvoice.discount_amount} RWF</span>
              </div>
              <div className="flex justify-between">
                <span>Tax:</span>
                <span>{generatedInvoice.tax_amount} RWF</span>
              </div>
              <div className="flex justify-between font-bold text-lg">
                <span>Total:</span>
                <span>{generatedInvoice.total_amount} RWF</span>
              </div>
            </div>
            
            {generatedInvoice.notes && (
              <div>
                <h3 className="font-semibold text-gray-900">Notes:</h3>
                <p className="text-gray-600">{generatedInvoice.notes}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default GenerateInvoice;
