import { useState, useEffect } from 'react';
import { adminService, invoiceService } from '../../services/endpoints';
import { INVOICE_STATUS } from '../../utils/constants';

const AdminInvoices = () => {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    fetchInvoices();
  }, []);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const response = await adminService.getInvoices();
      setInvoices(response.data || response);
    } catch (error) {
      setError('Failed to fetch invoices');
      console.error('Error fetching invoices:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInvoiceAction = async (invoiceId, status) => {
    try {
      setActionLoading(true);
      await adminService.updateInvoiceStatus(invoiceId, { status });
      await fetchInvoices();
      setShowModal(false);
      setSelectedInvoice(null);
    } catch (error) {
      setError(`Failed to update invoice status`);
      console.error('Error updating invoice:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteInvoice = async (invoiceId) => {
    if (!window.confirm('Are you sure you want to delete this invoice?')) {
      return;
    }

    try {
      setActionLoading(true);
      await invoiceService.deleteInvoice(invoiceId);
      await fetchInvoices();
      setShowModal(false);
      setSelectedInvoice(null);
    } catch (error) {
      setError('Failed to delete invoice');
      console.error('Error deleting invoice:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusColors = {
      DRAFT: 'bg-gray-100 text-gray-800',
      SENT: 'bg-blue-100 text-blue-800',
      PAID: 'bg-green-100 text-green-800',
      OVERDUE: 'bg-red-100 text-red-800',
      CANCELLED: 'bg-red-100 text-red-800'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[status] || 'bg-gray-100 text-gray-800'}`}>
        {status}
      </span>
    );
  };

  const filteredInvoices = invoices.filter(invoice => 
    filterStatus === 'all' || invoice.status === filterStatus
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Invoice Management</h1>
        <button
          onClick={fetchInvoices}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
        >
          Refresh
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex space-x-4">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Invoices</option>
            <option value="DRAFT">Draft</option>
            <option value="SENT">Sent</option>
            <option value="PAID">Paid</option>
            <option value="OVERDUE">Overdue</option>
            <option value="CANCELLED">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Invoices Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Invoice ID
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredInvoices.map((invoice) => (
              <tr key={invoice.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  INV-{invoice.id}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {invoice.customer_name || invoice.customer || 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  RWF {invoice.total_amount || invoice.amount || '0.00'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(invoice.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {new Date(invoice.created_at || Date.now()).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button
                    onClick={() => {
                      setSelectedInvoice(invoice);
                      setShowModal(true);
                    }}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    View
                  </button>
                  {invoice.status === 'DRAFT' && (
                    <button
                      onClick={() => handleInvoiceAction(invoice.id, 'SENT')}
                      className="text-green-600 hover:text-green-900"
                      disabled={actionLoading}
                    >
                      Send
                    </button>
                  )}
                  {invoice.status === 'SENT' && (
                    <button
                      onClick={() => handleInvoiceAction(invoice.id, 'PAID')}
                      className="text-green-600 hover:text-green-900"
                      disabled={actionLoading}
                    >
                      Mark Paid
                    </button>
                  )}
                  <button
                    onClick={() => handleDeleteInvoice(invoice.id)}
                    className="text-red-600 hover:text-red-900"
                    disabled={actionLoading}
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredInvoices.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">No invoices found</p>
          </div>
        )}
      </div>

      {/* Invoice Details Modal */}
      {showModal && selectedInvoice && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Invoice Details - INV-{selectedInvoice.id}
              </h3>
              
              <div className="space-y-3">
                <div>
                  <span className="font-medium">Status:</span> {getStatusBadge(selectedInvoice.status)}
                </div>
                <div>
                  <span className="font-medium">Customer:</span> {selectedInvoice.customer_name || selectedInvoice.customer || 'N/A'}
                </div>
                <div>
                  <span className="font-medium">Amount:</span> RWF {selectedInvoice.total_amount || selectedInvoice.amount || '0.00'}
                </div>
                <div>
                  <span className="font-medium">Date:</span> {new Date(selectedInvoice.created_at || Date.now()).toLocaleDateString()}
                </div>
                {selectedInvoice.description && (
                  <div>
                    <span className="font-medium">Description:</span> {selectedInvoice.description}
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Close
                </button>
                {selectedInvoice.status === 'DRAFT' && (
                  <button
                    onClick={() => handleInvoiceAction(selectedInvoice.id, 'SENT')}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    disabled={actionLoading}
                  >
                    Send Invoice
                  </button>
                )}
                {selectedInvoice.status === 'SENT' && (
                  <button
                    onClick={() => handleInvoiceAction(selectedInvoice.id, 'PAID')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    disabled={actionLoading}
                  >
                    Mark as Paid
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminInvoices;
