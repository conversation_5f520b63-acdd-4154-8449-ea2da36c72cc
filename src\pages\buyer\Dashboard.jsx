import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { buyerService } from '../../services/endpoints';
import DashboardStats from '../../components/DashboardStats';

const BuyerDashboard = () => {
  const [orders, setOrders] = useState([]);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch both orders and products
      const [ordersResponse, productsResponse] = await Promise.all([
        buyerService.getOrders(),
        buyerService.getProducts()
      ]);

      console.log('🏠 Buyer Dashboard: Orders response:', ordersResponse);
      console.log('🏠 Buyer Dashboard: Products response:', productsResponse);

      // Handle orders response
      const ordersData = ordersResponse.data || ordersResponse;
      setOrders(Array.isArray(ordersData) ? ordersData.slice(0, 5) : []);

      // Handle products response with enhanced structure detection
      let productsData = [];

      if (Array.isArray(productsResponse)) {
        productsData = productsResponse;
        console.log('🏠 Buyer Dashboard: Using direct array response');
      } else if (productsResponse?.data?.data && Array.isArray(productsResponse.data.data)) {
        // Nested data structure: response.data.data (this is our case!)
        productsData = productsResponse.data.data;
        console.log('🏠 Buyer Dashboard: Using response.data.data array (nested structure)');
      } else if (productsResponse?.data && Array.isArray(productsResponse.data)) {
        productsData = productsResponse.data;
        console.log('🏠 Buyer Dashboard: Using response.data array');
      } else if (productsResponse?.results && Array.isArray(productsResponse.results)) {
        productsData = productsResponse.results;
        console.log('🏠 Buyer Dashboard: Using response.results array');
      } else if (productsResponse?.products && Array.isArray(productsResponse.products)) {
        productsData = productsResponse.products;
        console.log('🏠 Buyer Dashboard: Using response.products array');
      } else if (typeof productsResponse === 'object' && productsResponse !== null) {
        const possibleArrays = Object.values(productsResponse).filter(Array.isArray);
        if (possibleArrays.length > 0) {
          productsData = possibleArrays[0];
          console.log('🏠 Buyer Dashboard: Found array in object properties');
        }
      }

      console.log('🏠 Buyer Dashboard: Final products data:', productsData);
      console.log('🏠 Buyer Dashboard: Products count:', productsData?.length);

      setProducts(Array.isArray(productsData) ? productsData : []);

    } catch (error) {
      console.error('🏠 Buyer Dashboard: Error fetching data:', error);
      setError('Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const stats = [
    {
      title: 'Available Products',
      value: products.length,
      icon: '🛒',
      color: 'bg-blue-500'
    },
    {
      title: 'Total Orders',
      value: orders.length,
      icon: '📋',
      color: 'bg-green-500'
    },
    {
      title: 'Pending Orders',
      value: orders.filter(order => order.status === 'PENDING').length,
      icon: '⏳',
      color: 'bg-yellow-500'
    },
    {
      title: 'Total Spent',
      value: `${orders.reduce((sum, order) => sum + (parseFloat(order.total_price || order.total_amount || 0)), 0).toLocaleString()} RWF`,
      icon: '💰',
      color: 'bg-purple-500'
    }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Buyer Dashboard</h1>
        <Link
          to="/buyer/order"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
        >
          Order Gas
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <DashboardStats stats={stats} />

      {/* Available Gas Products */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Available Gas Products
            </h3>
            <Link
              to="/buyer/order"
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Order Now →
            </Link>
          </div>

          {products.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-6xl mb-4">🛒</div>
              <p className="text-gray-500 text-lg mb-4">No gas products available</p>
              <p className="text-gray-400 mb-6">Check back later or contact support</p>
              <button
                onClick={fetchDashboardData}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
              >
                Refresh Products
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {products.slice(0, 6).map((product) => (
                <div key={product.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-gray-900">
                      {product.brand || product.gas_type || 'Gas Cylinder'}
                    </h4>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      (product.quantity || product.quantity_available || 0) > 10 ? 'bg-green-100 text-green-800' :
                      (product.quantity || product.quantity_available || 0) > 0 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {(product.quantity || product.quantity_available || 0) > 10 ? 'In Stock' :
                       (product.quantity || product.quantity_available || 0) > 0 ? 'Low Stock' : 'Out of Stock'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Weight: {product.weight_kg || product.weight || 'N/A'}kg
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    Available: {product.quantity || product.quantity_available || 0} units
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-gray-900">
                      {(product.unit_price || product.price || 0).toLocaleString()} RWF
                    </span>
                    <Link
                      to="/buyer/order"
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium"
                    >
                      Order
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}

          {products.length > 6 && (
            <div className="mt-4 text-center">
              <Link
                to="/buyer/order"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                View All Products ({products.length})
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Recent Orders */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Recent Orders
          </h3>
          
          {orders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No orders yet</p>
              <Link
                to="/buyer/order"
                className="mt-2 inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
              >
                Place Your First Order
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Gas Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.map((order) => (
                    <tr key={order.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        #{order.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {order.gas_type || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {order.quantity || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          order.status === 'DELIVERED' ? 'bg-green-100 text-green-800' :
                          order.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                          order.status === 'APPROVED' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        RWF {parseFloat(order.total_price || order.total_amount || 0).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(order.created_at).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {orders.length > 0 && (
            <div className="mt-4 text-center">
              <Link
                to="/buyer/orders"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                View All Orders
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BuyerDashboard;
