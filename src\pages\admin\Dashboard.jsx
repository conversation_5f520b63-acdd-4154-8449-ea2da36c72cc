import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { adminService } from '../../services/endpoints';
import DashboardStats from '../../components/DashboardStats';

const AdminDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    orders: [],
    users: [],
    invoices: [],
    totalRevenue: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Fetch data with individual error handling
      let ordersData = [];
      let usersData = [];
      let invoicesData = [];

      try {
        const ordersResponse = await adminService.getOrders();
        ordersData = Array.isArray(ordersResponse.data) ? ordersResponse.data : (Array.isArray(ordersResponse) ? ordersResponse : []);
      } catch (error) {
        console.warn('Failed to fetch orders:', error.message);
      }

      try {
        const usersResponse = await adminService.getUsers();
        usersData = Array.isArray(usersResponse.data) ? usersResponse.data : (Array.isArray(usersResponse) ? usersResponse : []);
      } catch (error) {
        console.warn('Failed to fetch users:', error.message);
      }

      try {
        const invoicesResponse = await adminService.getInvoices();
        invoicesData = Array.isArray(invoicesResponse.data) ? invoicesResponse.data : (Array.isArray(invoicesResponse) ? invoicesResponse : []);
      } catch (error) {
        console.warn('Failed to fetch invoices:', error.message);
      }

      setDashboardData({
        orders: ordersData,
        users: usersData,
        invoices: invoicesData,
        totalRevenue: ordersData.reduce((sum, order) => sum + (parseFloat(order.total_price || order.total_amount || 0)), 0)
      });
    } catch (error) {
      setError('Failed to fetch some dashboard data. Please check if the backend is running.');
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const stats = [
    {
      title: 'Total Orders',
      value: dashboardData.orders.length,
      icon: '📋',
      color: 'bg-blue-500'
    },
    {
      title: 'Total Users',
      value: dashboardData.users.length,
      icon: '👥',
      color: 'bg-green-500'
    },
    {
      title: 'Total Invoices',
      value: dashboardData.invoices.length,
      icon: '📄',
      color: 'bg-purple-500'
    },
    {
      title: 'Total Revenue',
      value: `${dashboardData.totalRevenue.toFixed(0)} RWF`,
      icon: '💰',
      color: 'bg-yellow-500'
    }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <div className="flex items-center space-x-3">
          <Link
            to="/debug"
            className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm"
          >
            API Debug
          </Link>
          <div className="text-sm text-gray-500">
            System Overview
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <DashboardStats stats={stats} />

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Recent Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Orders
            </h3>
            
            {dashboardData.orders.slice(0, 5).map((order) => (
              <div key={order.id} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                <div>
                  <p className="text-sm font-medium text-gray-900">Order #{order.id}</p>
                  <p className="text-xs text-gray-500">{order.gas_type}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{order.total_amount?.toFixed(0)} RWF</p>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                    order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {order.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* User Statistics */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              User Statistics
            </h3>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Buyers</span>
                <span className="text-sm font-medium text-gray-900">
                  {dashboardData.users.filter(user => user.role === 'buyer').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sellers</span>
                <span className="text-sm font-medium text-gray-900">
                  {dashboardData.users.filter(user => user.role === 'seller').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Admins</span>
                <span className="text-sm font-medium text-gray-900">
                  {dashboardData.users.filter(user => user.role === 'admin').length}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Order Status Distribution */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Order Status
            </h3>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Pending</span>
                <span className="text-sm font-medium text-yellow-600">
                  {dashboardData.orders.filter(order => order.status === 'pending').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Confirmed</span>
                <span className="text-sm font-medium text-blue-600">
                  {dashboardData.orders.filter(order => order.status === 'confirmed').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">In Progress</span>
                <span className="text-sm font-medium text-purple-600">
                  {dashboardData.orders.filter(order => order.status === 'in_progress').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Delivered</span>
                <span className="text-sm font-medium text-green-600">
                  {dashboardData.orders.filter(order => order.status === 'delivered').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Cancelled</span>
                <span className="text-sm font-medium text-red-600">
                  {dashboardData.orders.filter(order => order.status === 'cancelled').length}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Quick Actions
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <Link
              to="/admin/orders"
              className="bg-blue-50 hover:bg-blue-100 text-blue-700 p-4 rounded-lg text-center transition-colors block"
            >
              <div className="text-2xl mb-2">📋</div>
              <div className="text-sm font-medium">Manage Orders</div>
            </Link>

            <Link
              to="/admin/users"
              className="bg-green-50 hover:bg-green-100 text-green-700 p-4 rounded-lg text-center transition-colors block"
            >
              <div className="text-2xl mb-2">👥</div>
              <div className="text-sm font-medium">Manage Users</div>
            </Link>

            <Link
              to="/admin/invoices"
              className="bg-purple-50 hover:bg-purple-100 text-purple-700 p-4 rounded-lg text-center transition-colors block"
            >
              <div className="text-2xl mb-2">📄</div>
              <div className="text-sm font-medium">View Invoices</div>
            </Link>

            <Link
              to="/admin/locations"
              className="bg-orange-50 hover:bg-orange-100 text-orange-700 p-4 rounded-lg text-center transition-colors block"
            >
              <div className="text-2xl mb-2">📍</div>
              <div className="text-sm font-medium">Manage Locations</div>
            </Link>

            <Link
              to="/admin/reports"
              className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700 p-4 rounded-lg text-center transition-colors block"
            >
              <div className="text-2xl mb-2">📈</div>
              <div className="text-sm font-medium">View Reports</div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
